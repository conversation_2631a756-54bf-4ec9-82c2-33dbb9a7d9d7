import { useEffect, useState } from 'react'
import { cloneDeep, isEmpty } from 'lodash'
import { Stack, Typography } from '@karoo-ui/core'
import { match } from 'ts-pattern'

import { useSnackbarWithCloseAction } from 'src/components/Snackbar/Notistack/utils'
import { useAppDispatch } from 'src/redux-hooks'
import { ctIntl } from 'src/util-components/ctIntl'
import Spinner from 'src/util-components/spinner'

import {
  useSaveDashboardMaintenancePromptMutation,
  useSaveSisenseConfigurationMutation,
  useSisenseConfigurationQuery,
} from './api/queries'
import type { DashboardType, ReturnedSisenseConfiguration } from './api/types'
import useSisenseLoginAndAuth from './api/useSisenseLoginAndAuth'
import DashboardMaintenancePage from './components/DashboardMaintenancePage'
import { MaintenancePromptModal } from './components/DashboardMaintenancePage/DashboardMaintenancePromptModal'
import { setNewUser } from './components/SetupDashboard/slice'
import WaitingPage from './components/WaitingPage'

type Props = {
  dashboardType: DashboardType
  children: React.ReactNode
}

export default function DashboardComponentWrapper(props: Props) {
  const { dashboardType } = props
  const sisenseConfigurationQuery = useSisenseConfigurationQuery(dashboardType)

  return match(sisenseConfigurationQuery)
    .with({ status: 'pending' }, () => <Spinner />)
    .with({ status: 'error' }, () => null)
    .with({ status: 'success' }, (query) => (
      <DashboardComponentMaintenanceWrapper
        {...props}
        queryData={query.data}
      />
    ))
    .exhaustive()
}

const DashboardComponentMaintenanceWrapper = ({
  dashboardType,
  children,
  queryData,
}: { queryData: ReturnedSisenseConfiguration } & Props) => {
  const dispatch = useAppDispatch()
  const saveDashboardMaintenancePromptMutation =
    useSaveDashboardMaintenancePromptMutation({ dashboardType })
  const saveSisenseConfigurationMutation = useSaveSisenseConfigurationMutation()

  const {
    isDashboardAvailable,
    dashboardMaintenance,
    dashboardMaintenanceDate,
    dashboardMaintenancePrompt,
    dashboardConfiguration,
  } = queryData

  const [isMaintenancePromptOpen, setIsMaintenancePromptOpen] = useState(
    dashboardMaintenancePrompt,
  )

  const [maintenanceStartDate, maintenanceEndDate]: Array<string> | Array<null> =
    dashboardMaintenanceDate ? dashboardMaintenanceDate.split('|') : [null, null]

  // FIXME: if we refactor the dashboard in the future, we need to remove all these
  // saga codes from react-query
  const saveSisenseConfigurationMutate = saveSisenseConfigurationMutation.mutate
  useEffect(() => {
    if (dashboardMaintenance !== true && dashboardConfiguration === false) {
      dispatch(setNewUser(true))

      if (dashboardType === 'live') {
        saveSisenseConfigurationMutate({ dashboardType: 'live' })
      }
    }
  }, [
    dashboardConfiguration,
    dashboardMaintenance,
    dashboardType,
    dispatch,
    saveSisenseConfigurationMutate,
  ])

  if (isDashboardAvailable === false) {
    return <WaitingPage />
  } else if (dashboardMaintenance === true) {
    if (!dashboardMaintenanceDate || isEmpty(dashboardMaintenanceDate)) {
      return <DashboardMaintenancePage />
    } else {
      const [startDate, endDate] = dashboardMaintenanceDate.split('|')
      return (
        <DashboardMaintenancePage
          maintenanceStart={startDate}
          maintenanceEnd={endDate}
        />
      )
    }
  }

  return (
    <>
      {isMaintenancePromptOpen &&
        maintenanceStartDate !== null &&
        maintenanceEndDate !== null && (
          <MaintenancePromptModal
            warningMessageId={ctIntl.formatMessage({ id: 'Scheduled Maintenance' })}
            maintenanceStart={maintenanceStartDate}
            maintenanceEnd={maintenanceEndDate}
            isOpen={isMaintenancePromptOpen}
            onClose={() => {
              setIsMaintenancePromptOpen(false)
              saveDashboardMaintenancePromptMutation.mutate()
            }}
          />
        )}
      <DashboardComponentSisenseWrapper>{children}</DashboardComponentSisenseWrapper>
    </>
  )
}

const DashboardComponentSisenseWrapper = ({ children }: Pick<Props, 'children'>) => {
  const sisenseLoginAndAuth = useSisenseLoginAndAuth()
  const { enqueueSnackbarWithCloseAction } = useSnackbarWithCloseAction()

  useEffect(() => {
    /* NOTE: when Sisense script is loaded, it will override global window.L and apparently
    leaflet also uses window.L to initialize some stuff under the hood. That's why here I'm
    keeping the original value of window.L so that it can be assigned back
    to window.L when the dashboard unmounts to prevent HERE maps from crashing */
    const originalL = cloneDeep(window.L)

    return () => {
      window.L = originalL
    }
  }, [])

  const authTrigger = sisenseLoginAndAuth.trigger

  useEffect(() => {
    // NOTE: Here we force to send ct_sisense_login and isauth to server (auth&Login) everytime user enters dashboard module
    // but not do these after ct_login is 1. some user may not use dashboard module at all. 2. user may keep
    // opening dashboard modules days, so we need to force user to do auth&Login once enter dashboard
    authTrigger()
  }, [authTrigger])

  return match(sisenseLoginAndAuth)
    .with({ status: 'error' }, () => {
      enqueueSnackbarWithCloseAction(
        <Stack direction="column">
          <Typography variant="subtitle1">
            {ctIntl.formatMessage({ id: 'dashboard.authLogin.failure.title' })}
          </Typography>
          <Typography variant="body2">
            {ctIntl.formatMessage({ id: 'dashboard.authLogin.failure.content' })}
          </Typography>
        </Stack>,
        { variant: 'error' },
      )

      return null
    })
    .with({ status: 'pending' }, () => <Spinner />)
    .with({ status: 'success' }, () => children)
    .exhaustive()
}
