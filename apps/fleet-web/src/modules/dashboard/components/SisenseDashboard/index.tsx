import {
  useCallback,
  useEffect,
  useRef,
  useState,
  type ComponentType,
  type Dispatch,
  type SetStateAction,
} from 'react'
import { isNil } from 'lodash'
import * as R from 'remeda'
import styled from 'styled-components'
import { match } from 'ts-pattern'

import {
  getDashboardVehicleDisplayNameSetting,
  getLocale,
  getSettings_UNSAFE,
} from 'duxs/user'
import { useStopwatch } from 'src/hooks'
import { useEffectEvent } from 'src/hooks/useEventHandler'
import { useAppDispatch, useTypedSelector } from 'src/redux-hooks'
import { GA4 } from 'src/shared/google-analytics4'
import type { FixMeAny } from 'src/types'
import Spinner from 'src/util-components/spinner'
import { downloadHtmlElementByIdAsPdf } from 'src/util-functions/file-utils'
import {
  downloadWidgetsAsCSV,
  downloadWidgetsAsImages,
  isLongWidget,
  replaceDashboardDatasource,
} from 'src/util-functions/sisense'
import { capitalize } from 'src/util-functions/string-utils'

import {
  reportDashboardErrorToSentry,
  useFetchAllUnselectedWidgetsQuery,
  useSisenseConfigurationIfNeededQuery,
} from '../../api/queries'
import type { DashboardType, ReturnedSisenseConfiguration } from '../../api/types'
import {
  getIsDashboardInitiation,
  setIsDashboardInitiation,
} from '../SetupDashboard/slice'
import { useSaveDashboardLastSeenMutation } from './api/query'
import { sisenseConfig } from './config/sisense-config'
import DashboardFilters from './DashboardFilters'
import type { CoachingDashboardFilterProps } from './DashboardFilters/Coaching/Filters'
import CustomLiveDashboardFilters from './DashboardFilters/CustomLive'
import DeleteWidgetModal from './DeleteWidgetModal'
import NewWidgetOverlay from './NewWidgetOverlay'
import { saveDashboardSorting, type DashboardFilter } from './slice'
import type {
  DownloadFormat,
  ExposedMethodObjectFromFilterComponnet,
  JaqlQueries,
} from './types'
import Widget from './Widget'

const DATETIME_30_DAYS_AGO = Date.now() - 60 * 60 * 24 * 1000 * 30

type Props = {
  basePath: string
  dashboardType: DashboardType
  sisenseDataSourceToReplace:
    | { fullname: string; id: string; title: string; live: boolean }
    | { title: string; address: string }
    | null
  filterConfig:
    | {
        // NOTE: This is for the normal dashboard type, will handle the filterObject outside
        // for preserve filters when getting back from other subpages
        filterObject: DashboardFilter
        setFilterObject: Dispatch<SetStateAction<DashboardFilter>>
      }
    | {
        // NOTE: Currently this is used for Coaching, give an independent filter component
        filterComponent: ComponentType<CoachingDashboardFilterProps>
      }
}

const SisenseDashboard = (props: Props) => {
  const { dashboardType } = props
  const dashboardIndustry = useTypedSelector(getSettings_UNSAFE)?.dashboardIndustry
  const locale = useTypedSelector(getLocale)?.split('-')[0] ?? ''

  const sisenseConfigurationQuery = useSisenseConfigurationIfNeededQuery(dashboardType)
  // fetch all the unselected widgets
  const unselectedWidgetQuery = useFetchAllUnselectedWidgetsQuery({
    locale,
    industry: dashboardType === 'live' ? 0 : dashboardIndustry,
    dashboardType,
  })

  return match(sisenseConfigurationQuery)
    .with({ status: 'pending' }, () => <Spinner />)
    .with({ status: 'error' }, () => null)
    .with({ status: 'success' }, ({ data: configurations }) =>
      match(unselectedWidgetQuery)
        .with({ status: 'pending' }, () => <Spinner />)
        .with({ status: 'error' }, () => null)
        .with({ status: 'success' }, ({ data: unselectedWidgets }) => {
          // by default, treat the widgets created after last seen time as new widgets
          // if last seen not exist, set the duration as 30 days
          const numberOfNewWidgets = unselectedWidgets.filter(
            (widgetEntry) =>
              new Date(widgetEntry.createdAt || 0).getTime() >
              (configurations.dashboardLastSeen ?? DATETIME_30_DAYS_AGO),
          ).length

          return (
            configurations.mainDashboardId && (
              <SisenseDashboardWithConfiguration
                {...props}
                configurations={configurations}
                numOfNewWidgets={numberOfNewWidgets}
              />
            )
          )
        })
        .exhaustive(),
    )
    .exhaustive()
}

const SisenseDashboardWithConfiguration = ({
  configurations: {
    dashboardTooltip,
    dashboardSorting,
    mainDashboardId,
    fetchedWidgets,
  },
  basePath,
  numOfNewWidgets,
  dashboardType,
  filterConfig,
  sisenseDataSourceToReplace,
}: Props & {
  configurations: ReturnedSisenseConfiguration
  numOfNewWidgets: number
}) => {
  const dispatch = useAppDispatch()

  const { sisenseApi: sisenseURL } = useTypedSelector(getSettings_UNSAFE)

  const isDashboardInitiation = useTypedSelector(getIsDashboardInitiation)
  const vehicleDisplayName = useTypedSelector(getDashboardVehicleDisplayNameSetting)

  // NOTE: We need to sync the filters to sisense when all widgets become visible, but only once
  const [areFiltersInitialized, setAreFiltersInitialized] = useState(false)
  const filtersRef = useRef<ExposedMethodObjectFromFilterComponnet>(null)

  // dashboard widgets
  const [dashboardWidgets, setDashboardWidgets] = useState<Array<Record<string, any>>>(
    [],
  )
  const [dashboard, setDashboard] = useState<Record<string, any> | null>(null)
  const [widgetsToRender, setWidgetsToRender] = useState<Array<Record<string, any>>>([])
  const [widgetsToHide, setWidgetsToHide] = useState<Array<Record<string, any>>>([])
  const [areWidgetsLoading, setAreWidgetsLoading] = useState(false)
  const [startStopwatch, stopStopwatch] = useStopwatch()

  // drop and drag state
  const [isDragging, setIsDragging] = useState(false)
  const [draggedOverWidgets, setDraggedOverWidgets] = useState<Array<number>>([])
  const [draggingWidgetIndex, setDraggingWidgetIndex] = useState<number | null>(null)

  const [quickTourCurrentStep, setQuickTourCurrentStep] = useState(0)
  const [closedNewWidgetOverlay, setClosedNewWidgetOverlay] = useState(false)

  const [deleteWidgetModalData, setDeleteWidgetModalData] = useState<string | null>(
    null,
  )

  const saveDashboarrdLastSeenMutation = useSaveDashboardLastSeenMutation()

  const onMount = useEffectEvent(() => {
    if (mainDashboardId) {
      // NOTE: tricky way to make sure sisense send request to the correct sisense server
      // after getting the dashboard
      if (window.prism) {
        window.prism.origin = sisenseURL
      }
      // NOTE: sisenseApp is required initially, and we need to fetch it through `Sisense.connect`
      // to trigger the sisense internal `bootstrap` method to setup for the overlay element (such as tooltip)
      // And sisense connecting should only be launched once, multiple triggering may introduce some unexpected failure
      window.Sisense.connect(sisenseURL).then((app: Record<string, any>) => {
        app.dashboards
          .load(mainDashboardId)
          .then((dashboard: Record<string, any>) => {
            const fetchedWidgetsFromSisense = dashboard.widgets.$$widgets
            const dashboardModel = dashboard.$$model

            if (sisenseDataSourceToReplace) {
              replaceDashboardDatasource(dashboardModel, sisenseDataSourceToReplace)
            }

            const allWidgets = fetchedWidgetsFromSisense.filter(
              (widget: Record<string, any>) =>
                //Make sure this is not a blacklisted widget type
                !sisenseConfig.widgetTypesToExclude.includes(widget.type),
            )

            setDashboard(dashboard)
            setDashboardWidgets(allWidgets)

            stopStopwatch((lantency: number) =>
              GA4.event({
                category: 'Dashboard' + capitalize(dashboardType),
                action: `Dashboard loading time: ${lantency} ms`,
              }),
            )
          })
          .catch((error: FixMeAny) => {
            reportDashboardErrorToSentry({
              error,
              message: `Failed to load dashboard: ${mainDashboardId}`,
            })
          })
      })
    }
  })

  useEffect(() => {
    onMount()
  }, [])

  // NOTE: we should trigger the last seen mutation if
  // 1. there is no new widget
  // 2. there is new widgets and user has closed the new widget overlay
  const saveDashboarrdLastSeenMutate = saveDashboarrdLastSeenMutation.mutate
  useEffect(() => {
    if (numOfNewWidgets === 0 || (numOfNewWidgets > 0 && closedNewWidgetOverlay)) {
      saveDashboarrdLastSeenMutate({ dashboardType })
    }
  }, [
    numOfNewWidgets,
    closedNewWidgetOverlay,
    dashboardType,
    saveDashboarrdLastSeenMutate,
  ])

  useEffect(() => {
    startStopwatch()
  }, [startStopwatch])

  // generate the selected widgets of full props based on the fetchedWidgets
  const getSelectedWidgets = useCallback(
    () =>
      fetchedWidgets
        .map((widget) => {
          const widgetId =
            vehicleDisplayName === 'registration'
              ? widget.sisense_widget_id
              : widget.sisense_widget_id2 || widget.sisense_widget_id

          const targetWidget = dashboardWidgets.find((w) => widgetId === w.$$model.oid)

          if (isNil(targetWidget)) {
            return undefined
          }

          targetWidget.header = widget.widget_header
          if (widget.widget_name.toLowerCase().includes('converttime')) {
            const widgetNames = widget.widget_name.split(':').pop()
            if (widgetNames) targetWidget.columnsToConvert = widgetNames.split(',')
          }
          targetWidget.tooltipDescription = widget.help_description
          targetWidget.dashboardWidgetId = widget.dashboard_widget_id
          targetWidget.isHidden = false
          if (
            widget.sisense_widget_id2 &&
            widget.sisense_widget_id !== widget.sisense_widget_id2
          ) {
            targetWidget.isTogglingWidget = true
          } else {
            targetWidget.isTogglingWidget = false
          }

          return targetWidget
        })
        .filter((w) => R.isNonNullish(w)),
    [fetchedWidgets, vehicleDisplayName, dashboardWidgets],
  )

  // get the widgets to be hidden
  const getWidgetsToHide = useCallback(
    () =>
      fetchedWidgets.reduce<Array<Record<string, any>>>((acc, cur) => {
        if (
          cur.sisense_widget_id2 &&
          cur.sisense_widget_id !== cur.sisense_widget_id2
        ) {
          const widgetIdToHide =
            vehicleDisplayName === 'registration'
              ? cur.sisense_widget_id2
              : cur.sisense_widget_id
          const targetWidget = dashboardWidgets.find(
            (w) => widgetIdToHide === w.$$model.oid,
          )

          if (targetWidget) {
            targetWidget.header = cur.widget_header
            if (cur.widget_name.toLowerCase().includes('converttime')) {
              const widgetNames = cur.widget_name.split(':').pop()
              if (widgetNames) targetWidget.columnsToConvert = widgetNames.split(',')
            }
            targetWidget.tooltipDescription = cur.help_description
            targetWidget.dashboardWidgetId = cur.dashboard_widget_id
            targetWidget.isHidden = true
            targetWidget.isTogglingWidget = true

            return [...acc, targetWidget]
          }
        }

        return acc
      }, []),
    [fetchedWidgets, vehicleDisplayName, dashboardWidgets],
  )

  useEffect(() => {
    if (fetchedWidgets) {
      const selectedWidgets = getSelectedWidgets()
      const widgetsToHide = getWidgetsToHide()

      if (selectedWidgets?.length > 0) {
        let sortedWidgets: Array<Record<string, any>> | undefined = undefined

        if (
          dashboardSorting &&
          dashboardSorting.length > 2 &&
          selectedWidgets.length > 0
        ) {
          sortedWidgets = dashboardSorting
            .slice(1, dashboardSorting.length - 1)
            .split(',')
            .map((widgetId: string) => {
              const id = widgetId.trim()
              return selectedWidgets.find((widget) => widget.dashboardWidgetId === id)
            })
            .filter((w) => R.isNonNullish(w))
        }

        let finalWidgets = sortedWidgets ?? selectedWidgets

        if (isDashboardInitiation && finalWidgets.length > 0) {
          if (!finalWidgets.every((widget) => isLongWidget(widget.$$model.type))) {
            let smallWidgetCount = 0
            const smallWidgets: Array<Record<string, any>> = []
            const longWidgets: Array<Record<string, any>> = []
            for (const widget of finalWidgets) {
              if (isLongWidget(widget.$$model.type)) {
                longWidgets.push(widget)
              } else {
                smallWidgetCount += 1
                smallWidgets.push(widget)
              }
            }

            finalWidgets =
              smallWidgetCount % 3 === 0
                ? [...smallWidgets, ...longWidgets]
                : [...longWidgets, ...smallWidgets]
          }

          const orderedWidgetList = finalWidgets.map((w) => w.dashboardWidgetId)

          dispatch(saveDashboardSorting({ widgets: orderedWidgetList, dashboardType }))
          dispatch(setIsDashboardInitiation(false))
        }
        setWidgetsToRender(finalWidgets)
      }

      setWidgetsToHide(widgetsToHide)
    }
  }, [
    dashboardSorting,
    fetchedWidgets,
    getWidgetsToHide,
    vehicleDisplayName,
    getSelectedWidgets,
    isDashboardInitiation,
    dispatch,
    dashboardType,
  ])

  // FIXME: It is kind of tricky to do this, can remove this if we find a better way
  // to avoid applying filters when widgets are visible
  useEffect(() => {
    if (
      widgetsToRender.length > 0 &&
      widgetsToRender.every((widget) => widget.visible) &&
      areFiltersInitialized === false
    ) {
      setAreFiltersInitialized(true)
    }
  }, [areFiltersInitialized, widgetsToRender])

  // NOTE: the reason of using useEffect here is widgets would be updated by sisense.js implicitly
  // we need to monitor their visible states to update the loading status
  useEffect(() => {
    setAreWidgetsLoading(widgetsToRender.every((widget) => !widget.visible))
  }, [widgetsToRender])

  /************************************************ Drag and Drop *************************************************/

  const handleDragOverWidget = useCallback((indice: Array<number>) => {
    setDraggedOverWidgets(indice)
  }, [])

  const handleDragStart = (index: number) => {
    setIsDragging(true)
    setDraggingWidgetIndex(index)
  }

  const handleDrop = (index: number) => {
    setIsDragging(false)
    setDraggedOverWidgets([])

    const clonedWidgetsToRender = widgetsToRender.slice()
    const items = clonedWidgetsToRender.splice(draggingWidgetIndex as number, 1)

    if (index === draggingWidgetIndex) {
      return
    } else if ((draggingWidgetIndex as number) > index) {
      clonedWidgetsToRender.splice(index, 0, ...items)
    } else {
      clonedWidgetsToRender.splice(index - 1, 0, ...items)
    }
    const orderedWidgetList = clonedWidgetsToRender.map((w) => w.dashboardWidgetId)

    setWidgetsToRender(clonedWidgetsToRender)
    dispatch(saveDashboardSorting({ widgets: orderedWidgetList, dashboardType }))
  }

  const handleDragEnd = () => {
    setDraggedOverWidgets([])
    setIsDragging(false)
  }

  const handleDeleteWidget = (dashboardWidgetId: string) =>
    setDeleteWidgetModalData(dashboardWidgetId)

  const handleDownloadWidgets = (format: DownloadFormat) => {
    if (format === 'Image') {
      downloadWidgetsAsImages(widgetsToRender)
    } else if (format === 'CSV') {
      downloadWidgetsAsCSV(widgetsToRender, sisenseURL)
    } else if (format === 'PDF') {
      downloadHtmlElementByIdAsPdf({
        elementId: 'widget-container',
        fileName: 'dashboard.pdf',
        displaySvg: false,
      })
    }
  }

  const handleClearSelection = (filter: JaqlQueries) => {
    // NOTE: call the exposed method inside filter component to clear all selection
    // combine the filters from widgets and update them together
    filtersRef.current?.clearSelections(filter)
  }

  const showOverlay = numOfNewWidgets > 0 && !closedNewWidgetOverlay

  const renderFilterComponentBasedInConfig = () => {
    if ('filterComponent' in filterConfig)
      return (
        <filterConfig.filterComponent
          // NOTE: bind the update method's this since it's used internally
          updateMethod={dashboard?.$$model.filters.update.bind(
            dashboard?.$$model.filters,
          )}
          initializeSisenseFilters={areFiltersInitialized}
          imperativeRef={filtersRef}
          isDownloadButtonDisabled={areWidgetsLoading}
          handleDownloadWidgets={handleDownloadWidgets}
        />
      )

    if (dashboardType === 'custom_live')
      return (
        <CustomLiveDashboardFilters
          {...filterConfig}
          basePath={basePath}
          // NOTE: bind the update method's this since it's used internally
          updateMethod={dashboard?.$$model.filters.update.bind(
            dashboard?.$$model.filters,
          )}
          initializeSisenseFilters={areFiltersInitialized}
          imperativeRef={filtersRef}
          dashboardType={dashboardType}
          widgetsNumber={widgetsToRender.length}
          isDownloadButtonDisabled={areWidgetsLoading}
          handleDownloadWidgets={handleDownloadWidgets}
          quickTourCurrentStep={quickTourCurrentStep}
          setQuickTourCurrentStep={setQuickTourCurrentStep}
        />
      )

    return (
      <DashboardFilters
        {...filterConfig}
        basePath={basePath}
        // NOTE: bind the update method's this since it's used internally
        updateMethod={dashboard?.$$model.filters.update.bind(
          dashboard?.$$model.filters,
        )}
        initializeSisenseFilters={areFiltersInitialized}
        imperativeRef={filtersRef}
        dashboardType={dashboardType}
        widgetsNumber={widgetsToRender.length}
        isDownloadButtonDisabled={areWidgetsLoading}
        handleDownloadWidgets={handleDownloadWidgets}
        quickTourCurrentStep={quickTourCurrentStep}
        setQuickTourCurrentStep={setQuickTourCurrentStep}
      />
    )
  }
  return (
    // NOTE: It's crucial to have this node here to allow sisense to load the app object
    <Container
      data-testid={`Dashboard-SisenseDashboard-${dashboardType}`}
      id="sisenseApp"
      dashboardTooltip={dashboardTooltip}
    >
      {deleteWidgetModalData !== null && (
        <DeleteWidgetModal
          dashboardType={dashboardType}
          onClose={() => setDeleteWidgetModalData(null)}
          widgetId={deleteWidgetModalData}
        />
      )}
      {showOverlay && (
        <NewWidgetOverlay
          onCloseOverlay={() => setClosedNewWidgetOverlay(true)}
          numOfNewWidgets={numOfNewWidgets}
        />
      )}
      {renderFilterComponentBasedInConfig()}
      <div style={{ zIndex: 0 }}>
        <WidgetContainer id="widget-container">
          {areWidgetsLoading && (
            <SpinnerBlock>
              <Spinner />
            </SpinnerBlock>
          )}
          {[...widgetsToRender, ...widgetsToHide].map((widget, index: number) => (
            <Widget
              key={widget.id}
              widget={widget}
              index={index}
              onDragStart={handleDragStart}
              isDragging={isDragging}
              onDragOverWidget={handleDragOverWidget}
              draggedOverWidgets={draggedOverWidgets}
              onDrop={handleDrop}
              draggingWidgetIndex={draggingWidgetIndex}
              onDeleteWidget={handleDeleteWidget}
              onDragEnd={handleDragEnd}
              sisenseURL={sisenseURL}
              setQuickTourCurrentStep={setQuickTourCurrentStep}
              quickTourCurrentStep={quickTourCurrentStep}
              onClearSelection={handleClearSelection}
              dashboardType={dashboardType}
              dashboardSorting={dashboardSorting}
            />
          ))}
        </WidgetContainer>
      </div>
    </Container>
  )
}

export default SisenseDashboard

const Container = styled.div<{ dashboardTooltip?: boolean }>`
  width: 100%;
  position: relative;

  /* temporarily hide tooltips & context menus
  on widgets due to CSS issues from Sisense,
  will remove this when Sisense resolves their issues */

  .menu-popup,
  .tipper-host {
    display: ${(props) => (props.dashboardTooltip ? 'block' : 'none !important')};
  }
`

const WidgetContainer = styled.div`
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  margin: 0 auto;
  padding-left: 20px;
  position: relative;
`

const SpinnerBlock = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: calc(100vh - 220px);
  z-index: 10;
  background-color: #f9f9f9;
`
